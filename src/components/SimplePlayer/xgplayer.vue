<template>
  <div :id="playerId" class="xg-player-container"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import Player from 'xgplayer'
import HlsPlugin from 'xgplayer-hls'
import 'xgplayer/dist/index.min.css'

defineOptions({ name: 'SimplePlayer' })

interface Props {
  src: string
  controls?: boolean
  autoplay?: boolean
  loop?: boolean
  volume?: number
  poster?: string
  width?: string | number
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  controls: true,
  autoplay: false,
  loop: false,
  volume: 0.6,
  poster: '',
  width: '100%',
  height: '100%'
})

const playerId = `xgplayer-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
let player: any = null

const initPlayer = () => {
  if (!props.src) return

  const isHls = props.src.includes('.m3u8')
  const plugins = isHls ? [HlsPlugin] : []

  const config = {
    id: playerId,
    url: props.src,
    width: props.width,
    height: props.height,
    autoplay: props.autoplay,
    loop: props.loop,
    volume: props.volume,
    controls: props.controls,
    poster: props.poster,
    playsinline: true,
    isLive: isHls,
    plugins: plugins
  }

  player = new Player(config)
}

const destroyPlayer = () => {
  if (player) {
    player.destroy()
    player = null
  }
}

watch(() => props.src, async () => {
  destroyPlayer()
  await nextTick()
  initPlayer()
})

onMounted(() => {
  initPlayer()
})

onBeforeUnmount(() => {
  destroyPlayer()
})

defineExpose({
  player: () => player
})
</script>

<style scoped>
.xg-player-container {
  width: 100%;
  height: 100%;
}

:deep(.xgplayer) {
  width: 100% !important;
  height: 100% !important;
}
</style>
