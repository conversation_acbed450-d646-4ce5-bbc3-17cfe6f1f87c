<template>
  <video
    ref="videoRef"
    :src="src"
    :controls="controls"
    :autoplay="autoplay"
    :loop="loop"
    :muted="muted"
    :poster="poster"
    :width="width"
    :height="height"
    class="simple-player"
    crossorigin="anonymous"
    preload="auto"
    playsinline
    @loadstart="handleLoadStart"
    @canplay="handleCanPlay"
    @error="handleError"
  >
    您的浏览器不支持视频播放
  </video>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'

defineOptions({ name: 'SimplePlayer' })

interface Props {
  src: string
  controls?: boolean
  autoplay?: boolean
  loop?: boolean
  volume?: number
  muted?: boolean
  poster?: string
  width?: string | number
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  controls: true,
  autoplay: false,
  loop: false,
  volume: 0.6,
  muted: false,
  poster: '',
  width: '100%',
  height: '100%'
})

const videoRef = ref<HTMLVideoElement>()

const setVolume = () => {
  if (videoRef.value) {
    videoRef.value.volume = props.volume
  }
}

const handleLoadStart = () => {
  console.log('视频开始加载')
}

const handleCanPlay = () => {
  console.log('视频可以播放')
  setVolume()
}

const handleError = (event: Event) => {
  console.error('视频播放错误:', event)
}

watch(() => props.volume, setVolume)

onMounted(() => {
  setVolume()
})

defineExpose({
  videoElement: () => videoRef.value
})
</script>

<style scoped>
.simple-player {
  width: 100%;
  height: 100%;
  background-color: #000;
}
</style>
