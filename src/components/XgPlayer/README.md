# XgPlayer 西瓜播放器组件

## 简介

XgPlayer 是基于西瓜播放器的 Vue 3 组件，专门用于解决直播流播放问题。支持 HLS (m3u8) 和 FLV 格式的直播流，避免了 video.js 的 XMLHttpRequest 错误问题。

## 特性

- ✅ 支持 HLS (m3u8) 直播流
- ✅ 支持 FLV 直播流  
- ✅ 支持 MP4 普通视频
- ✅ 自动检测视频格式并加载对应插件
- ✅ 完善的错误处理
- ✅ 响应式设计
- ✅ 动态加载，按需引入

## 使用方法

### 基础用法

```vue
<template>
  <XgPlayer
    :src="videoUrl"
    :controls="true"
    :autoplay="false"
    :volume="0.6"
    width="100%"
    height="400px"
  />
</template>

<script setup>
import XgPlayer from '@/components/XgPlayer/index.vue'

const videoUrl = 'http://live.songtaiwang.cn/live/songtai-alg.m3u8'
</script>
```

### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| src | string | - | 视频源地址 (必填) |
| width | number/string | '100%' | 播放器宽度 |
| height | number/string | '100%' | 播放器高度 |
| autoplay | boolean | false | 是否自动播放 |
| loop | boolean | false | 是否循环播放 |
| volume | number | 0.6 | 音量 (0-1) |
| controls | boolean | true | 是否显示控制栏 |
| poster | string | '' | 封面图片 |

### 支持的视频格式

- **HLS (.m3u8)**: 适用于直播流
- **FLV (.flv)**: 适用于直播流
- **MP4 (.mp4)**: 适用于点播视频

### 方法

```vue
<template>
  <XgPlayer ref="playerRef" :src="videoUrl" />
</template>

<script setup>
import { ref } from 'vue'

const playerRef = ref()

// 获取播放器实例
const getPlayer = () => {
  return playerRef.value?.player()
}
</script>
```

## 解决的问题

原来使用 video.js 播放 m3u8 直播流时会出现以下错误：
```
InvalidStateError: Failed to read the 'responseText' property from 'XMLHttpRequest': 
The value is only accessible if the object's 'responseType' is '' or 'text' (was 'arraybuffer').
```

XgPlayer 通过使用西瓜播放器替代 video.js，完美解决了这个问题。
