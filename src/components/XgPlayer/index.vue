<template>
  <div ref="playerContainer" class="xg-player-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'

defineOptions({ name: 'XgPlayer' })

interface Props {
  src: string
  width?: number | string
  height?: number | string
  autoplay?: boolean
  loop?: boolean
  volume?: number
  controls?: boolean
  poster?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  autoplay: false,
  loop: false,
  volume: 0.6,
  controls: true,
  poster: ''
})

const playerContainer = ref<HTMLElement>()
let player: any = null

// 检测视频格式
const getVideoType = (url: string) => {
  if (url.includes('.m3u8')) {
    return 'hls'
  } else if (url.includes('.flv')) {
    return 'flv'
  } else {
    return 'mp4'
  }
}

// 初始化播放器
const initPlayer = async () => {
  if (!playerContainer.value || !props.src) return

  try {
    // 动态导入西瓜播放器
    const { default: Player } = await import('xgplayer')
    
    const videoType = getVideoType(props.src)
    
    // 根据视频类型加载对应插件
    if (videoType === 'hls') {
      await import('xgplayer-hls')
    } else if (videoType === 'flv') {
      await import('xgplayer-flv')
    }

    // 播放器配置
    const config = {
      el: playerContainer.value,
      url: props.src,
      width: props.width,
      height: props.height,
      autoplay: props.autoplay,
      loop: props.loop,
      volume: props.volume,
      controls: props.controls,
      poster: props.poster,
      // 根据视频类型设置播放器类型
      ...(videoType === 'hls' && { isLive: true }),
      ...(videoType === 'flv' && { isLive: true }),
      // 错误处理
      errorTips: '视频加载失败，请检查网络连接',
      // 语言设置
      lang: 'zh-cn'
    }

    player = new Player(config)

    // 监听播放器事件
    player.on('error', (error: any) => {
      console.error('XgPlayer播放错误:', error)
    })

    player.on('loadstart', () => {
      console.log('XgPlayer开始加载')
    })

    player.on('canplay', () => {
      console.log('XgPlayer可以播放')
    })

  } catch (error) {
    console.error('初始化XgPlayer失败:', error)
  }
}

// 销毁播放器
const destroyPlayer = () => {
  if (player) {
    try {
      player.destroy()
      player = null
    } catch (error) {
      console.error('销毁XgPlayer失败:', error)
    }
  }
}

// 监听src变化
watch(() => props.src, async (newSrc) => {
  if (newSrc) {
    destroyPlayer()
    await nextTick()
    initPlayer()
  } else {
    destroyPlayer()
  }
})

onMounted(() => {
  if (props.src) {
    initPlayer()
  }
})

onBeforeUnmount(() => {
  destroyPlayer()
})

// 暴露播放器实例
defineExpose({
  player: () => player
})
</script>

<style scoped>
.xg-player-container {
  width: 100%;
  height: 100%;
}

/* 西瓜播放器样式覆盖 */
:deep(.xgplayer) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.xgplayer-error) {
  background-color: #f5f7fa;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
</style>
