<template>
  <div class="video-player-wrapper">
    <video
      ref="videoRef"
      :src="props.src"
      :width="props.width"
      :height="props.height"
      :autoplay="props.autoplay"
      :loop="props.loop"
      :controls="props.controls"
      :poster="props.poster"
      :muted="!props.volume"
      crossorigin="anonymous"
      preload="auto"
      playsinline
      class="video-player"
      @loadstart="handleLoadStart"
      @canplay="handleCanPlay"
      @error="handleError"
      @waiting="handleWaiting"
      @playing="handlePlaying"
    >
      您的浏览器不支持视频播放
    </video>
    <div v-if="showError" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'

defineOptions({ name: 'XgPlayer' })

interface Props {
  src: string
  width?: number | string
  height?: number | string
  autoplay?: boolean
  loop?: boolean
  volume?: number
  controls?: boolean
  poster?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  autoplay: false,
  loop: false,
  volume: 0.6,
  controls: true,
  poster: ''
})

const videoRef = ref<HTMLVideoElement>()
const showError = ref(false)
const errorMessage = ref('')

// 设置音量
const setVolume = () => {
  if (videoRef.value) {
    videoRef.value.volume = props.volume
  }
}

// 事件处理
const handleLoadStart = () => {
  console.log('视频开始加载')
  showError.value = false
}

const handleCanPlay = () => {
  console.log('视频可以播放')
  setVolume()
}

const handleError = (event: Event) => {
  console.error('视频播放错误:', event)
  showError.value = true
  errorMessage.value = '视频加载失败，请检查网络连接或直播流地址'
}

const handleWaiting = () => {
  console.log('视频缓冲中')
}

const handlePlaying = () => {
  console.log('视频正在播放')
}

// 监听src变化
watch(
  () => props.src,
  () => {
    showError.value = false
  }
)

// 监听音量变化
watch(() => props.volume, setVolume)

onMounted(() => {
  setVolume()
})

// 暴露视频元素
defineExpose({
  videoElement: () => videoRef.value
})
</script>

<style scoped>
.xg-player-container {
  width: 100%;
  height: 100%;
}

/* 西瓜播放器样式覆盖 */
:deep(.xgplayer) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.xgplayer-error) {
  background-color: #f5f7fa;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
</style>
