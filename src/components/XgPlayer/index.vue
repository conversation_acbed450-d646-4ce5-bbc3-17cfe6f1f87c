<template>
  <div ref="playerContainer" class="xg-player-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'

defineOptions({ name: 'XgPlayer' })

interface Props {
  src: string
  width?: number | string
  height?: number | string
  autoplay?: boolean
  loop?: boolean
  volume?: number
  controls?: boolean
  poster?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  autoplay: false,
  loop: false,
  volume: 0.6,
  controls: true,
  poster: ''
})

const playerContainer = ref<HTMLElement>()
let player: any = null

// 检测视频格式
const getVideoType = (url: string) => {
  if (url.includes('.m3u8')) {
    return 'hls'
  } else if (url.includes('.flv')) {
    return 'flv'
  } else {
    return 'mp4'
  }
}

// 初始化播放器
const initPlayer = async () => {
  if (!playerContainer.value || !props.src) return

  try {
    const videoType = getVideoType(props.src)

    // 按照官网示例的方式使用全局变量
    const Player = (window as any).Player
    const FlvPlayer = (window as any).FlvPlayer
    const HlsPlayer = (window as any).HlsPlayer

    if (!Player) {
      console.error('XgPlayer未加载，请检查CDN资源')
      return
    }

    // 根据视频类型配置插件
    const plugins = []
    if (videoType === 'flv' && FlvPlayer) {
      plugins.push(FlvPlayer)
    } else if (videoType === 'hls' && HlsPlayer) {
      plugins.push(HlsPlayer)
    }

    // 播放器配置
    const config = {
      id: playerContainer.value,
      url: props.src,
      width: props.width,
      height: props.height,
      autoplay: props.autoplay,
      loop: props.loop,
      volume: props.volume,
      controls: props.controls,
      poster: props.poster,
      playsinline: true,
      isLive: videoType === 'hls' || videoType === 'flv',
      plugins: plugins
    }

    player = new Player(config)

    // 监听播放器事件
    player.on('error', (error: any) => {
      console.error('XgPlayer播放错误:', error)
    })

    player.on('loadstart', () => {
      console.log('XgPlayer开始加载')
    })

    player.on('canplay', () => {
      console.log('XgPlayer可以播放')
    })
  } catch (error) {
    console.error('初始化XgPlayer失败:', error)
  }
}

// 销毁播放器
const destroyPlayer = () => {
  if (player) {
    try {
      player.destroy()
      player = null
    } catch (error) {
      console.error('销毁XgPlayer失败:', error)
    }
  }
}

// 监听src变化
watch(
  () => props.src,
  async (newSrc) => {
    if (newSrc) {
      destroyPlayer()
      await nextTick()
      initPlayer()
    } else {
      destroyPlayer()
    }
  }
)

onMounted(() => {
  if (props.src) {
    initPlayer()
  }
})

onBeforeUnmount(() => {
  destroyPlayer()
})

// 暴露播放器实例
defineExpose({
  player: () => player
})
</script>

<style scoped>
.xg-player-container {
  width: 100%;
  height: 100%;
}

/* 西瓜播放器样式覆盖 */
:deep(.xgplayer) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.xgplayer-error) {
  background-color: #f5f7fa;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
</style>
