<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑视频流' : '创建视频流'"
    width="700px"
    :before-close="handleClose"
    append-to-body
  >
    <div class="dialog-content">
      <!-- 左侧视频预览 -->
      <div class="video-player-container">
        <XgPlayer
          v-if="videoSource"
          :src="videoSource"
          :volume="0.6"
          :controls="true"
          :autoplay="true"
          :loop="true"
          width="100%"
          height="100%"
          class="video-player"
        />
        <div v-else class="no-video">请输入直播流地址以预览</div>
      </div>

      <!-- 右侧表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="left"
        class="form-container"
      >
        <el-form-item label="摄像头名称" prop="deviceName">
          <el-input v-model="formData.deviceName" placeholder="请输入摄像头名称" />
        </el-form-item>
        <el-form-item label="需要认证">
          <el-switch v-model="requiresAuth" />
        </el-form-item>
        <template v-if="requiresAuth">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="formData.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <div class="password-group">
              <el-input
                v-model="formData.password"
                type="password"
                show-password
                placeholder="请输入密码"
                class="form-input"
              />
              <el-button type="primary" size="small" class="test-button" @click="handleTest">
                测试
              </el-button>
            </div>
          </el-form-item>
        </template>
        <el-form-item label="直播流地址" prop="liveUrl">
          <el-input
            v-model="formData.liveUrl"
            placeholder="请输入直播流地址"
            @input="updateVideoSource"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleFastFill">快速填充</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { createDevice, updateDevice } from '@/api/alg/device'
import type { ResourceDevice } from '../types'
import XgPlayer from '@/components/XgPlayer/index.vue'

defineOptions({ name: 'VideoStreamCreateDialog' })

const emit = defineEmits<{
  success: []
}>()

type FormData = Partial<ResourceDevice>

const getInitialData = (): FormData => ({
  id: undefined,
  deviceName: undefined,
  username: undefined,
  password: undefined,
  liveUrl: undefined
})

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const formData = reactive<FormData>(getInitialData())
const isEdit = ref(false)
const videoSource = ref('')
const requiresAuth = ref(false)

const formRules: FormRules = {
  deviceName: [{ required: true, message: '请输入摄像头名称', trigger: 'blur' }],
  liveUrl: [{ required: true, message: '请输入直播流地址', trigger: 'blur' }]
}

const updateVideoSource = (url: string) => {
  videoSource.value = url
}

const handleFastFill = () => {
  requiresAuth.value = true
  formData.deviceName = '测试摄像头'
  formData.username = 'admin'
  formData.password = 'admin123'
  formData.liveUrl =
    'https://sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/hls/xgplayer-demo.m3u8'
  updateVideoSource(formData.liveUrl)
}

/** 测试连接 */
const handleTest = async () => {
  if (!formData.liveUrl) {
    ElMessage.warning('请输入直播流地址')
    return
  }
  // 这里只是一个前端的模拟，实际的测试应该调用后端API
  ElMessage.success('连接测试成功（模拟）')
}

/** 打开弹窗 */
const open = (device?: FormData) => {
  dialogVisible.value = true
  resetForm()
  if (device) {
    isEdit.value = true
    Object.assign(formData, device)
    if (formData.liveUrl) {
      videoSource.value = formData.liveUrl
    }
    requiresAuth.value = !!device.username
  } else {
    isEdit.value = false
    requiresAuth.value = false
  }
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  Object.assign(formData, getInitialData())
  videoSource.value = ''
  requiresAuth.value = false
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  const isValid = await formRef.value.validate().catch(() => false)
  if (!isValid) return

  loading.value = true
  try {
    if (isEdit.value) {
      await updateDevice(formData as ResourceDevice)
      ElMessage.success('更新成功')
    } else {
      await createDevice(formData as ResourceDevice)
      ElMessage.success('创建成功')
    }
    handleClose()
    emit('success')
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    console.error(isEdit.value ? '更新视频流失败:' : '创建视频流失败:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  open
})
</script>

<style scoped>
.dialog-content {
  display: flex;
  gap: 20px;
}
.video-player-container {
  width: 320px;
  height: 180px;
  border: 1px solid #eee;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}
.video-player {
  width: 100%;
  height: 100%;
}
.no-video {
  color: #909399;
}
.form-container {
  flex: 1;
}
.password-group {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}
.form-input {
  flex: 1;
}
.test-button {
  height: 32px;
  padding: 8px 16px;
  border-radius: 4px;
  background-color: #0057d9;
  border: none;
  font-size: 14px;
  color: #ffffff;
  flex-shrink: 0;
}
.test-button:hover {
  background-color: #0046b3;
}
.dialog-footer {
  text-align: right;
}
</style>
