import * as MP4Box from 'mp4box'

export interface MP4Info {
  duration: number // 时长（秒）
  timescale: number // 时间刻度
  isFragmented: boolean // 是否为分片文件
  isProgressive: boolean // 是否为渐进式下载
  hasIOD: boolean // 是否有IOD
  brands: string[] // 品牌信息
  created: Date // 创建时间
  modified: Date // 修改时间
  tracks: MP4TrackInfo[] // 轨道信息
  videoTracks: MP4VideoTrackInfo[] // 视频轨道
  audioTracks: MP4AudioTrackInfo[] // 音频轨道
}

export interface MP4TrackInfo {
  id: number
  created: Date
  modified: Date
  movie_duration: number
  layer: number
  alternate_group: number
  volume: number
  track_width: number
  track_height: number
  timescale: number
  duration: number
  bitrate: number
  codec: string
  language: string
  nb_samples: number
}

export interface MP4VideoTrackInfo extends MP4TrackInfo {
  video: {
    width: number
    height: number
  }
}

export interface MP4AudioTrackInfo extends MP4TrackInfo {
  audio: {
    sample_rate: number
    channel_count: number
    sample_size: number
  }
}

export class MP4Analyzer {
  constructor() {
    // 构造函数暂时为空，每次分析时创建新的MP4Box实例
  }

  /**
   * 分析MP4文件
   * @param file File对象或ArrayBuffer
   * @returns Promise<MP4Info>
   */
  async analyzeFile(file: File | ArrayBuffer): Promise<MP4Info> {
    return new Promise((resolve, reject) => {
      const mp4boxfile = MP4Box.createFile()

      // 设置事件监听器
      mp4boxfile.onError = (error: any) => {
        console.error('MP4Box解析错误:', error)
        reject(new Error(`MP4解析失败: ${error.message || error}`))
      }

      mp4boxfile.onReady = (info: any) => {
        try {
          const mp4Info = this.parseMP4Info(info)
          console.log('MP4文件信息:', mp4Info)
          resolve(mp4Info)
        } catch (error) {
          console.error('解析MP4信息时出错:', error)
          reject(error)
        }
      }

      // 处理文件数据
      if (file instanceof File) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const arrayBuffer = e.target?.result as ArrayBuffer
          if (arrayBuffer) {
            this.processArrayBuffer(mp4boxfile, arrayBuffer)
          } else {
            reject(new Error('无法读取文件内容'))
          }
        }
        reader.onerror = () => {
          reject(new Error('文件读取失败'))
        }
        reader.readAsArrayBuffer(file)
      } else {
        this.processArrayBuffer(mp4boxfile, file)
      }
    })
  }

  private processArrayBuffer(mp4boxfile: any, arrayBuffer: ArrayBuffer) {
    try {
      // 将ArrayBuffer转换为MP4Box需要的格式
      const buffer = arrayBuffer as any
      buffer.fileStart = 0

      // 开始解析
      mp4boxfile.appendBuffer(buffer)
      mp4boxfile.flush()
    } catch (error) {
      console.error('处理ArrayBuffer时出错:', error)
      throw error
    }
  }

  private parseMP4Info(info: any): MP4Info {
    const videoTracks: MP4VideoTrackInfo[] = []
    const audioTracks: MP4AudioTrackInfo[] = []
    const allTracks: MP4TrackInfo[] = []

    // 解析轨道信息
    if (info.tracks) {
      info.tracks.forEach((track: any) => {
        const baseTrack: MP4TrackInfo = {
          id: track.id,
          created: new Date(track.created),
          modified: new Date(track.modified),
          movie_duration: track.movie_duration,
          layer: track.layer,
          alternate_group: track.alternate_group,
          volume: track.volume,
          track_width: track.track_width,
          track_height: track.track_height,
          timescale: track.timescale,
          duration: track.duration,
          bitrate: track.bitrate,
          codec: track.codec,
          language: track.language,
          nb_samples: track.nb_samples
        }

        allTracks.push(baseTrack)

        // 视频轨道
        if (track.video) {
          videoTracks.push({
            ...baseTrack,
            video: {
              width: track.video.width,
              height: track.video.height
            }
          } as MP4VideoTrackInfo)
        }

        // 音频轨道
        if (track.audio) {
          audioTracks.push({
            ...baseTrack,
            audio: {
              sample_rate: track.audio.sample_rate,
              channel_count: track.audio.channel_count,
              sample_size: track.audio.sample_size
            }
          } as MP4AudioTrackInfo)
        }
      })
    }

    return {
      duration: info.duration / info.timescale, // 转换为秒
      timescale: info.timescale,
      isFragmented: info.isFragmented,
      isProgressive: info.isProgressive,
      hasIOD: info.hasIOD,
      brands: info.brands || [],
      created: new Date(info.created),
      modified: new Date(info.modified),
      tracks: allTracks,
      videoTracks,
      audioTracks
    }
  }

  /**
   * 检查MP4文件是否可播放
   * @param mp4Info MP4文件信息
   * @returns 检查结果
   */
  static checkPlayability(mp4Info: MP4Info): {
    canPlay: boolean
    issues: string[]
    recommendations: string[]
  } {
    const issues: string[] = []
    const recommendations: string[] = []

    // 检查是否有视频轨道
    if (mp4Info.videoTracks.length === 0) {
      issues.push('没有找到视频轨道')
    }

    // 检查视频编码
    mp4Info.videoTracks.forEach((track, index) => {
      const codec = track.codec.toLowerCase()
      if (
        !codec.includes('avc') &&
        !codec.includes('h264') &&
        !codec.includes('h265') &&
        !codec.includes('vp9')
      ) {
        issues.push(`视频轨道${index + 1}使用了不常见的编码格式: ${track.codec}`)
        recommendations.push('建议使用H.264编码格式以获得更好的兼容性')
      }
    })

    // 检查音频编码
    mp4Info.audioTracks.forEach((track, index) => {
      const codec = track.codec.toLowerCase()
      if (!codec.includes('aac') && !codec.includes('mp3')) {
        issues.push(`音频轨道${index + 1}使用了不常见的编码格式: ${track.codec}`)
        recommendations.push('建议使用AAC编码格式以获得更好的兼容性')
      }
    })

    // 检查文件结构
    if (mp4Info.isFragmented) {
      issues.push('这是一个分片MP4文件，可能不支持拖拽播放')
      recommendations.push('建议使用渐进式MP4格式以支持流式播放')
    }

    return {
      canPlay: issues.length === 0,
      issues,
      recommendations
    }
  }

  /**
   * 格式化MP4信息为可读字符串
   * @param mp4Info MP4文件信息
   * @returns 格式化的信息字符串
   */
  static formatInfo(mp4Info: MP4Info): string {
    const lines: string[] = []

    lines.push(`=== MP4文件信息 ===`)
    lines.push(`时长: ${Math.round(mp4Info.duration)}秒`)
    lines.push(`品牌: ${mp4Info.brands.join(', ')}`)
    lines.push(`创建时间: ${mp4Info.created.toLocaleString()}`)
    lines.push(`是否分片: ${mp4Info.isFragmented ? '是' : '否'}`)
    lines.push(`是否渐进式: ${mp4Info.isProgressive ? '是' : '否'}`)

    if (mp4Info.videoTracks.length > 0) {
      lines.push(`\n=== 视频轨道 ===`)
      mp4Info.videoTracks.forEach((track, index) => {
        lines.push(`轨道${index + 1}:`)
        lines.push(`  分辨率: ${track.video.width}x${track.video.height}`)
        lines.push(`  编码: ${track.codec}`)
        lines.push(`  比特率: ${Math.round(track.bitrate / 1000)}kbps`)
        lines.push(`  帧数: ${track.nb_samples}`)
      })
    }

    if (mp4Info.audioTracks.length > 0) {
      lines.push(`\n=== 音频轨道 ===`)
      mp4Info.audioTracks.forEach((track, index) => {
        lines.push(`轨道${index + 1}:`)
        lines.push(`  采样率: ${track.audio.sample_rate}Hz`)
        lines.push(`  声道数: ${track.audio.channel_count}`)
        lines.push(`  编码: ${track.codec}`)
        lines.push(`  比特率: ${Math.round(track.bitrate / 1000)}kbps`)
      })
    }

    return lines.join('\n')
  }
}

// 创建单例实例
export const mp4Analyzer = new MP4Analyzer()
